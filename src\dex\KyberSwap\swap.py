#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
KyberSwap CLI工具
使用KyberSwap聚合器API执行代币交换，支持原生代币，包装/解包装，以及ERC20代币间交换
"""

import os
import sys
import json
import asyncio
import click
import yaml
from web3 import Web3
from typing import Dict, Optional

from .client import KyberSwapClient
from .constants import SUPPORTED_CHAINS

def get_token_address(chain: str, token: str) -> str:
    """
    获取代币地址，支持符号或直接使用地址
    
    Args:
        chain: 链名称
        token: 代币符号或地址
        
    Returns:
        代币地址
    """
    # 如果已经是地址格式，直接返回
    if token.startswith("0x") and len(token) == 42:
        return token
        
    if chain not in SUPPORTED_CHAINS:
        raise ValueError(f"不支持的链: {chain}")
        
    # 处理原生代币
    chain_info = SUPPORTED_CHAINS[chain]
    if token.upper() == chain_info["symbol"]:
        return chain_info["native_token_address"]
        
    # 处理包装的原生代币
    if token.upper() == f"W{chain_info['symbol']}":
        return chain_info["wrapped_token_address"]
        
    # 查找常用代币
    if token.upper() in chain_info["common_tokens"]:
        return chain_info["common_tokens"][token.upper()]
        
    raise ValueError(f"未知的代币: {token}，在{chain}链上")

def get_token_decimals(chain: str, token_address: str) -> int:
    """
    获取代币精度 (小数位数)
    
    Args:
        chain: 链名称
        token_address: 代币地址
        
    Returns:
        代币精度 (小数位数)
    """
    # 文件路径
    decimals_file_path = os.path.join("data", "utils", "token", "gate_tokens_with_decimals.json")
    
    # 将chain转换为对应的网络名称
    network_map = {
        "ethereum": "ethereum",
        "polygon": "polygon",
        "bsc": "bsc",
        "base": "base"
    }
    
    network = network_map.get(chain.lower())
    token_address_lower = token_address.lower()
    
    # 尝试从gate_tokens_with_decimals.json读取代币精度
    try:
        # 判断文件是否存在
        if os.path.exists(decimals_file_path):
            with open(decimals_file_path, 'r', encoding='utf-8') as f:
                tokens_data = json.load(f)
                
            if network and network in tokens_data:
                # 在对应网络中查找代币精度
                for token_info in tokens_data[network]:
                    if "contract_address" in token_info and token_info["contract_address"].lower() == token_address_lower:
                        if "decimals" in token_info:
                            print(f"✅ 从gate_tokens_with_decimals.json读取到代币精度: {token_info['decimals']}")
                            return token_info["decimals"]
    except Exception as e:
        print(f"⚠️ 从文件读取代币精度时出错: {str(e)}")
    
    # 如果从文件中找不到，使用原来的方法
    
    # USDT等稳定币通常是6位精度
    common_stablecoins = {
        "polygon": {
            "******************************************": 6,  # USDT (Polygon)
            "******************************************": 6,  # USDC (Polygon)
            "******************************************": 18,  # DAI (Polygon)
        },
        "ethereum": {
            "******************************************": 6,  # USDT (Ethereum)
            "******************************************": 6,  # USDC (Ethereum)
        },
        "bsc": {
            "******************************************": 18,  # USDT (BSC)
            "******************************************": 18,  # USDC (BSC)
        },
        "base": {
            "******************************************": 6,  # USDC (Base)
            "******************************************": 18,  # DAI (Base)
            "******************************************": 6,  # USDbC (Base)
        }
    }
    
    # 如果是已知稳定币，直接返回对应精度
    if chain in common_stablecoins:
        if token_address_lower in common_stablecoins[chain]:
            decimals = common_stablecoins[chain][token_address_lower]
            # 更新到文件中
            update_token_decimals(token_address, decimals, network)
            return decimals
    
    # 对于原生代币和包装代币，返回18位精度
    if chain in SUPPORTED_CHAINS and (
        token_address.lower() == SUPPORTED_CHAINS[chain]["native_token_address"].lower() or
        token_address.lower() == SUPPORTED_CHAINS[chain]["wrapped_token_address"].lower()
    ):
        # 更新到文件中
        update_token_decimals(token_address, 18, network)
        return 18
    
    try:
        # 创建Web3实例以便调用合约
        config = load_config()
        rpc_url = None
        
        # 尝试从配置中获取RPC URL
        if "dex" in config and chain in config["dex"]:
            chain_config = config["dex"][chain]
            if "rpc_url" in chain_config:
                rpc_url = chain_config["rpc_url"]
            elif "rpc" in chain_config and "endpoint" in chain_config["rpc"]:
                rpc_url = chain_config["rpc"]["endpoint"]
                
        if not rpc_url:
            if chain == "ethereum":
                rpc_url = "https://eth.llamarpc.com"
            elif chain == "polygon":
                rpc_url = "https://polygon.llamarpc.com"
            elif chain == "bsc":
                rpc_url = "https://binance.llamarpc.com"
            elif chain == "base":
                rpc_url = "https://base.llamarpc.com"
            else:
                print(f"⚠️ 无法获取 {chain} 的RPC URL，使用默认精度18")
                return 18
                
        # 初始化Web3
        web3 = Web3(Web3.HTTPProvider(rpc_url))
        
        # 创建代币合约实例
        token_contract = web3.eth.contract(
            address=web3.to_checksum_address(token_address),
            abi=[{
                "constant": True,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "type": "function"
            }]
        )
        
        # 获取代币精度
        decimals = token_contract.functions.decimals().call()
        print(f"✅ 通过链上调用获取代币精度: {decimals}")
        
        # 更新到文件中
        update_token_decimals(token_address, decimals, network)
        
        return decimals
    except Exception as e:
        print(f"⚠️ 无法获取代币精度，使用默认值18: {str(e)}")
        # 使用默认精度18并更新到文件
        update_token_decimals(token_address, 18, network)
        return 18  # 默认精度

def update_token_decimals(token_address: str, decimals: int, network: str) -> None:
    """
    将获取到的代币精度更新到gate_tokens_with_decimals.json文件中
    
    Args:
        token_address: 代币地址
        decimals: 代币精度
        network: 网络名称 (ETH, MATIC, BSC)
    """
    if not network:
        print("⚠️ 无法确定网络名称，跳过更新代币精度")
        return
        
    # 文件路径
    decimals_file_path = os.path.join("data", "utils", "token", "gate_tokens_with_decimals.json")
    
    try:
        # 读取现有数据
        tokens_data = {}
        if os.path.exists(decimals_file_path):
            with open(decimals_file_path, 'r', encoding='utf-8') as f:
                tokens_data = json.load(f)
        
        # 确保网络键存在
        if network not in tokens_data:
            tokens_data[network] = []
            
        # 检查代币是否已存在
        token_address_lower = token_address.lower()
        token_exists = False
        
        for token_info in tokens_data[network]:
            if "contract_address" in token_info and token_info["contract_address"].lower() == token_address_lower:
                # 如果代币已存在但没有decimals字段或值不同，则更新
                if "decimals" not in token_info or token_info["decimals"] != decimals:
                    token_info["decimals"] = decimals
                    print(f"💾 更新代币精度: {token_address} => {decimals}")
                token_exists = True
                break
        
        # 如果代币不存在，添加新记录
        if not token_exists:
            # 尝试获取代币符号
            symbol = get_token_symbol(token_address, network)
            new_token = {
                "symbol": symbol if symbol else f"TOKEN_{token_address[:6]}",
                "contract_address": token_address,
                "decimals": decimals
            }
            tokens_data[network].append(new_token)
            print(f"💾 添加新代币精度: {token_address} => {decimals}")
            
        # 写回文件
        with open(decimals_file_path, 'w', encoding='utf-8') as f:
            json.dump(tokens_data, f, indent=2, ensure_ascii=False)
            
    except Exception as e:
        print(f"⚠️ 更新代币精度到文件时出错: {str(e)}")
        
def get_token_symbol(token_address: str, network: str) -> str:
    """
    获取代币符号
    
    Args:
        token_address: 代币地址
        network: 网络名称 (ethereum, polygon, bsc)
        
    Returns:
        str: 代币符号，如果获取失败则返回None
    """
    try:
        # 反向映射网络名称到链名称
        chain_map = {
            "ethereum": "ethereum",
            "polygon": "polygon",
            "bsc": "bsc"
        }
        chain = chain_map.get(network)
        
        if not chain or chain not in SUPPORTED_CHAINS:
            return None
            
        chain_config = SUPPORTED_CHAINS[chain]
        
        # 对于原生代币，返回链符号
        if token_address.lower() == chain_config["native_token_address"].lower():
            return chain_config["symbol"]
            
        # 对于包装代币，返回W+链符号
        if token_address.lower() == chain_config["wrapped_token_address"].lower():
            return f"W{chain_config['symbol']}"
        
        # 检查常用代币列表
        for symbol, addr in chain_config["common_tokens"].items():
            if addr.lower() == token_address.lower():
                return symbol
        
        # 通过合约调用获取符号
        # 创建Web3实例
        config = load_config()
        rpc_url = None
        
        # 尝试从配置中获取RPC URL
        if "dex" in config and chain in config["dex"]:
            chain_cfg = config["dex"][chain]
            if "rpc_url" in chain_cfg:
                rpc_url = chain_cfg["rpc_url"]
            elif "rpc" in chain_cfg and "endpoint" in chain_cfg["rpc"]:
                rpc_url = chain_cfg["rpc"]["endpoint"]
                
        if not rpc_url:
            if chain == "ethereum":
                rpc_url = "https://eth.llamarpc.com"
            elif chain == "polygon":
                rpc_url = "https://polygon.llamarpc.com"
            elif chain == "bsc":
                rpc_url = "https://bsc-dataseed.binance.org"
            else:
                return None
                
        # 初始化Web3
        web3 = Web3(Web3.HTTPProvider(rpc_url))
        
        # 创建代币合约实例
        token_contract = web3.eth.contract(
            address=web3.to_checksum_address(token_address),
            abi=[{
                "constant": True,
                "inputs": [],
                "name": "symbol",
                "outputs": [{"name": "", "type": "string"}],
                "type": "function"
            }]
        )
        
        # 获取符号
        symbol = token_contract.functions.symbol().call()
        return symbol
    except Exception as e:
        print(f"⚠️ 获取代币符号时出错: {str(e)}")
        return None

def load_config() -> Dict:
    """
    加载配置文件
    
    Returns:
        配置字典
    """
    config_path = os.path.join("config", "config.yaml")
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        sys.exit(1)

def format_token_info(amount: str, token: str, chain: str, raw_amount: Optional[int] = None) -> str:
    """
    格式化代币信息显示
    
    Args:
        amount: 代币数量
        token: 代币符号或地址
        chain: 链名称
        raw_amount: 原始数量（以最小单位表示，用于精确计算）
        
    Returns:
        格式化后的字符串
    """
    token_address = token
    if not token.startswith("0x"):
        # 转换符号为地址
        try:
            token_address = get_token_address(chain, token)
        except:
            token_address = token
    
    # 尝试获取更友好的代币符号显示
    display_token = token
    if token.startswith("0x"):
        # 尝试查找符号
        for symbol, addr in SUPPORTED_CHAINS[chain]["common_tokens"].items():
            if addr.lower() == token.lower():
                display_token = symbol
                break
        
        if display_token == token:
            display_token = f"{token[:6]}...{token[-4:]}"
    else:
        display_token = token.upper()
    
    # 如果提供了原始数量，按照代币精度重新格式化
    if raw_amount is not None:
        decimals = get_token_decimals(chain, token_address)
        formatted_amount = raw_amount / (10 ** decimals)
        return f"{formatted_amount} {display_token}"
    
    return f"{amount} {display_token}"

# 创建一个装饰器来处理异步函数
def coro(f):
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper

@click.group()
def cli():
    """KyberSwap代币交换工具"""
    pass

@cli.command(name="swap")
@click.option("--chain", required=True, help="链名称 (polygon, ethereum, bsc)")
@click.option("--token-in", required=True, help="输入代币地址或符号")
@click.option("--token-out", required=True, help="输出代币地址或符号")
@click.option("--amount", required=True, type=float, help="输入代币数量")
@click.option("--slippage", default=1.0, type=float, help="滑点容忍度（百分比，如0.5表示0.5%)")
@click.option("--save-gas/--no-save-gas", default=False, help="是否优先考虑节省gas")
@click.option("--real/--simulate", default=False, help="是否执行真实交易（否则仅模拟）")
@click.option("--timeout", default=60, type=int, help="交易超时时间（秒）")
@click.option("--gas-multiplier", default=1.8, type=float, help="Gas估计值乘数，默认1.8倍")
@click.option("--excluded-sources", help="排除的流动性来源，逗号分隔，例如: paraswap,0x。注意：bebop已默认排除")
@click.option("--include-bebop/--exclude-bebop", default=False, help="是否包含bebop路由，默认排除")
@click.option("--recipient", help="代币接收地址，默认为交易发送者地址")
@click.pass_context
def swap_command(ctx, chain, token_in, token_out, amount, slippage, save_gas, real, timeout, gas_multiplier, excluded_sources, include_bebop, recipient):
    """执行代币交换"""
    try:
        # 初始化客户端
        private_key = os.environ.get("PRIVATE_KEY")
        
        # 如果环境变量中没有私钥，尝试从配置文件获取
        if not private_key:
            try:
                config = load_config()
                if chain in config["dex"]:
                    # 适配两种配置路径格式
                    if "private_key" in config["dex"][chain]:
                        private_key = config["dex"][chain]["private_key"]
                    elif "wallet" in config["dex"][chain] and "private_key" in config["dex"][chain]["wallet"]:
                        private_key = config["dex"][chain]["wallet"]["private_key"]
            except Exception as e:
                print(f"从配置文件加载私钥失败: {str(e)}")
        
        if real and not private_key:
            print("❌ 错误: 执行实际交易需要设置PRIVATE_KEY环境变量或在配置文件中配置私钥")
            return
            
        # 处理排除的来源
        final_excluded_sources = excluded_sources or ""
        if not include_bebop:
            if final_excluded_sources:
                # 检查是否已经包含bebop
                excluded_list = final_excluded_sources.split(",")
                if "bebop" not in [s.strip().lower() for s in excluded_list]:
                    final_excluded_sources += ",bebop"
            else:
                final_excluded_sources = "bebop"
            
        # 显示交易信息
        print(f"📊 准备交换:")
        print(f"   从: {amount} {token_in}")
        print(f"   至: {token_out}")
        print(f"   链: {chain.capitalize()}")
        print(f"   滑点: {slippage}%")
        print(f"   Gas优化: {'启用' if save_gas else '禁用'}")
        print(f"   交易截止时间: {timeout}秒")
        print(f"   Gas乘数: {gas_multiplier}倍")
        if final_excluded_sources:
            print(f"   排除来源: {final_excluded_sources}")
        if recipient:
            print(f"   代币接收地址: {recipient}")
        
        # 处理各种代币输入形式
        token_in_address, token_out_address, is_native_in, is_native_out, amount_wei = handle_amount_and_chain(
            ctx, chain, token_in, token_out, amount
        )
        
        # 创建客户端
        client = KyberSwapClient(chain, private_key)
        
        # 使用asyncio运行交换
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(
            client.swap(
                token_in=token_in_address,
                token_out=token_out_address,
                amount_in=amount_wei,
                slippage=slippage,
                is_native_in=is_native_in,
                confirm=True,
                save_gas=save_gas,
                deadline_seconds=timeout,
                simulate=not real,
                gas_multiplier=gas_multiplier,
                excluded_sources=final_excluded_sources,
                recipient=recipient
            )
        )
        
        # 处理结果
        if result["status"] == "成功":
            print(f"✅ {result['status']}: {result['tx_hash']}")
            print(f"   {result['message']}")
            
            # 如果有交易回执，检查最终状态
            if "receipt" in result:
                if result["receipt"]["status"] == 1:
                    print(f"🟢 交易已成功确认并执行")
                else:
                    print(f"🔴 警告: 交易已上链但执行失败，请在区块链浏览器检查详情")
                    print(f"   查看交易: https://{chain}scan.com/tx/{result['tx_hash']}")
        elif result["status"] == "模拟成功":
            print(f"⚠️ {result['status']}: {result['message']}")
        elif result["status"] == "未知":
            print(f"⚠️ {result['status']}: {result['message']}")
            print(f"   请手动检查交易状态: https://{chain}scan.com/tx/{result['tx_hash']}")
        else:
            print(f"❌ {result['status']}: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 交易失败: {str(e)}")
        if hasattr(e, "__traceback__"):
            import traceback
            traceback.print_tb(e.__traceback__)

def handle_amount_and_chain(ctx, chain, token_in, token_out, amount):
    """
    处理代币输入和链，统一格式化输入参数
    
    Args:
        ctx: Click上下文
        chain: 链名称
        token_in: 输入代币地址或符号
        token_out: 输出代币地址或符号
        amount: 代币数量
        
    Returns:
        tuple: (token_in_address, token_out_address, is_native_in, is_native_out, amount_wei)
    """
    # 输入代币标准化（如MATIC/POL → 链的原生代币符号）
    if token_in.upper() in ["polygon", "POL"] and chain.lower() == "polygon":
        token_in = "polygon"
    elif token_in.upper() == "ethereum" and chain.lower() in ["ethereum", "base"]:
        token_in = "ethereum"
    elif token_in.upper() == "BNB" and chain.lower() == "bsc":
        token_in = "BNB"
        
    # 获取代币地址
    token_in_address = get_token_address(chain, token_in)
    token_out_address = get_token_address(chain, token_out)
    
    # 获取代币精度
    token_in_decimals = get_token_decimals(chain, token_in_address)
    amount_in_wei = str(int(amount * (10 ** token_in_decimals)))
    
    # 检查是否为原生代币
    is_native_in = token_in_address.lower() == SUPPORTED_CHAINS[chain]["native_token_address"].lower()
    is_native_out = token_out_address.lower() == SUPPORTED_CHAINS[chain]["native_token_address"].lower()
    
    # 显示详细的代币信息
    print(f"\n🔍 代币详情:")
    print(f"   输入代币: {token_in} ({token_in_address})")
    print(f"   输出代币: {token_out} ({token_out_address})")
    print(f"   是否为原生代币输入: {is_native_in}")
    print(f"   是否为原生代币输出: {is_native_out}")
    print(f"   是否为包装代币输入: {token_in_address.lower() == SUPPORTED_CHAINS[chain]['wrapped_token_address'].lower()}")
    print(f"   是否为包装代币输出: {token_out_address.lower() == SUPPORTED_CHAINS[chain]['wrapped_token_address'].lower()}")
    
    return token_in_address, token_out_address, is_native_in, is_native_out, amount_in_wei

@cli.command(name="quote")
@click.option("--chain", required=True, help="链名称 (polygon, ethereum, bsc)")
@click.option("--token-in", required=True, help="输入代币地址或符号")
@click.option("--token-out", required=True, help="输出代币地址或符号")
@click.option("--amount", required=True, type=float, help="输入代币数量")
@click.option("--slippage", default=1.0, type=float, help="滑点容忍度（百分比，如0.5表示0.5%)")
@click.option("--save-gas/--no-save-gas", default=False, help="是否优先考虑节省gas")
@click.option("--gas-multiplier", default=3.0, type=float, help="Gas估计值乘数，默认3.0倍")
@click.option("--excluded-sources", help="排除的流动性来源，逗号分隔，例如: paraswap,0x。注意：bebop已默认排除")
@click.option("--include-bebop/--exclude-bebop", default=False, help="是否包含bebop路由，默认排除")
@click.pass_context
def quote_command(ctx, chain, token_in, token_out, amount, slippage, save_gas, gas_multiplier, excluded_sources, include_bebop):
    """获取代币交换报价"""
    try:
        # 处理排除的来源
        final_excluded_sources = excluded_sources or ""
        if not include_bebop:
            if final_excluded_sources:
                # 检查是否已经包含bebop
                excluded_list = final_excluded_sources.split(",")
                if "bebop" not in [s.strip().lower() for s in excluded_list]:
                    final_excluded_sources += ",bebop"
            else:
                final_excluded_sources = "bebop"
        
        # 显示交易信息
        print(f"📊 准备获取报价:")
        print(f"   从: {amount} {token_in}")
        print(f"   至: {token_out}")
        print(f"   链: {chain.capitalize()}")
        print(f"   滑点: {slippage}%")
        print(f"   Gas优化: {'启用' if save_gas else '禁用'}")
        print(f"   Gas乘数: {gas_multiplier}倍")
        if final_excluded_sources:
            print(f"   排除来源: {final_excluded_sources}")
        
        # 处理各种代币输入形式
        token_in_address, token_out_address, is_native_in, is_native_out, amount_wei = handle_amount_and_chain(
            ctx, chain, token_in, token_out, amount
        )
        
        # 创建客户端
        client = KyberSwapClient(chain=chain)
        
        # 获取路由
        print(f"\n🔍 获取报价...")
        loop = asyncio.get_event_loop()
        routes_data = loop.run_until_complete(
            client.get_routes(
                token_in=token_in_address,
                token_out=token_out_address,
                amount_in=amount_wei,
                slippage=slippage,
                is_native_in=is_native_in,
                save_gas=save_gas,
                excluded_sources=final_excluded_sources,
                use_proxy=True  # 获取报价时使用代理
            )
        )
        
        if "error" in routes_data:
            print(f"❌ 获取报价失败: {routes_data['error']}")
            return
        
        # 检查是否有路由数据
        if "data" not in routes_data or "routeSummary" not in routes_data["data"]:
            print(f"❌ 未找到可用交易路径")
            return
            
        # 获取路由摘要
        route_summary = routes_data["data"]["routeSummary"]
        
        # 分析路由复杂度
        route_complexity = 0
        if "route" in route_summary:
            for route_path in route_summary["route"]:
                route_complexity += len(route_path)
            print(f"⚠️ 路由复杂度: {route_complexity} 跳")
        
        # 获取输出代币精度
        token_out_decimals = get_token_decimals(chain, token_out_address)
        
        # 计算输出金额
        output_amount = int(route_summary["amountOut"])
        output_formatted = output_amount / (10 ** token_out_decimals)
        
        # 计算价格影响
        price_impact = 0
        if 'amountInUsd' in route_summary and 'amountOutUsd' in route_summary:
            amount_in_usd = float(route_summary['amountInUsd'])
            amount_out_usd = float(route_summary['amountOutUsd'])
            if amount_in_usd > 0:
                price_impact = (amount_in_usd - amount_out_usd) / amount_in_usd * 100
        
        # 显示交易预览
        print(f"\n📊 报价详情:")
        print(f"   输入: {amount} {token_in}")
        print(f"   预计输出: {output_formatted} {token_out}")
        
        if 'amountInUsd' in route_summary and 'amountOutUsd' in route_summary:
            print(f"   价值: ${route_summary.get('amountInUsd')} → ${route_summary.get('amountOutUsd')}")
            print(f"   价格影响: {price_impact:.2f}%")
            
        # 动态计算gas限制 - 使用预估Gas的3倍，并设置一个最低底线
        estimated_gas = int(route_summary.get("gas", 200000))  # 默认值200000
        min_gas_limit = 650000  # 最低gas限制底线
        
        # 根据路由复杂度调整乘数
        adjusted_multiplier = gas_multiplier

        # 获取token_in和token_out的小写地址用于比较
        token_in_lower = token_in_address.lower() if isinstance(token_in_address, str) else ""
        token_out_lower = token_out_address.lower() if isinstance(token_out_address, str) else ""

        # USDT地址
        usdt_addresses = [
            "******************************************".lower(),  # Ethereum USDT
            "******************************************".lower()   # Polygon USDT
        ]

        # 检查是否为USDT交易
        is_usdt_involved = any(addr in token_in_lower or addr in token_out_lower for addr in usdt_addresses)

        # 调整乘数
        if route_complexity > 2:
            adjusted_multiplier = max(gas_multiplier, 3.5)  # 复杂路由使用更高的乘数
            print(f"路径复杂度: {route_complexity} 步骤, 增加gas乘数至 {adjusted_multiplier}")
        elif is_usdt_involved:
            adjusted_multiplier = max(gas_multiplier, 4.0)  # USDT交易使用更高的乘数
            print(f"USDT相关交易, 使用更高gas乘数: {adjusted_multiplier}")
        
        # 计算最终gas限制
        gas_limit = max(int(estimated_gas * adjusted_multiplier), min_gas_limit)
        
        # 获取当前gas价格和优化后的价格
        base_gas_price = client.web3.eth.gas_price
        gas_price = int(base_gas_price * 1.4)  # 使用1.4倍的gas价格
        
        # 直接使用KyberSwapClient对象获取ETH价格和计算gas成本
        try:
            # 计算Gas成本 (Wei)
            gas_cost_wei = estimated_gas * gas_price
            
            # 获取ETH价格
            eth_price = 0
            # 如果client是KyberSwapClient实例，使用loop.run_until_complete获取ETH价格
            if isinstance(client, KyberSwapClient):
                loop = asyncio.get_event_loop()
                eth_price = loop.run_until_complete(client.get_eth_price_from_geckoterminal())
            else:
                # 使用默认价格
                eth_price = 1800.0
                print("使用默认ETH价格: $1800.0")
            
            # 计算基础Gas成本
            base_gas_cost_usd = (gas_cost_wei / 10**18) * eth_price
            
            # 应用1.4倍安全系数
            final_gas_cost_usd = base_gas_cost_usd * 1.4
            
            print(f"使用ETH价格 ${eth_price}")
            print(f"预估基础gas成本: ${base_gas_cost_usd:.4f}")
            print(f"最终gas成本(含1.4倍安全系数): ${final_gas_cost_usd:.4f}")
            
            # 使用含安全系数的值作为最终gas成本
            gas_cost_usd = final_gas_cost_usd
        except Exception as e:
            print(f"计算gas成本时出错: {str(e)}")
            gas_cost_usd = None

        print(f"   预估Gas: {estimated_gas}")
        print(f"   建议Gas限制: {gas_limit} (预估: {estimated_gas}, 乘数: {adjusted_multiplier}倍)")
        print(f"   Gas价格: {gas_price / 10**9} Gwei (原始: {base_gas_price / 10**9} Gwei, 乘数: 1.4倍)")

        # 显示Gas成本的USD等值
        if gas_cost_usd is not None:
            print(f"   预估Gas成本: ${gas_cost_usd:.4f} USDT")
            
        # 显示路由合约地址
        if "routerAddress" in routes_data["data"]:
            print(f"\n📋 路由合约: {routes_data['data']['routerAddress']}")
            
        # 显示完整路由信息
        if "route" in route_summary:
            print(f"\n🔄 交易路径:")
            for i, route_path in enumerate(route_summary["route"]):
                print(f"   路径 {i+1}:")
                for j, hop in enumerate(route_path):
                    protocol = hop.get("exchange", "未知")
                    if j == 0:
                        print(f"     {hop['tokenIn']} → {hop['tokenOut']} ({protocol})")
                    else:
                        print(f"     → {hop['tokenOut']} ({protocol})")
            
    except Exception as e:
        print(f"❌ 获取报价失败: {str(e)}")
        if hasattr(e, "__traceback__"):
            import traceback
            traceback.print_tb(e.__traceback__)

@cli.command(name="check-approval")
@click.option("--chain", required=True, help="区块链网络 (例如: polygon, ethereum, bsc)")
@click.option("--token", required=True, help="代币符号或地址")
@click.option("--amount", required=True, type=float, help="需要授权的代币数量")
@click.option("--private-key", help="私钥 (可选，默认从配置文件加载)")
@coro
async def check_approval_command(chain: str, token: str, amount: float, private_key: Optional[str]):
    """
    检查代币授权状态，如果未授权可以执行授权操作
    
    例如:
    python -m src.dex.KyberSwap.swap check-approval --chain polygon --token USDC --amount 10
    """
    try:
        # 加载配置
        config = load_config()
        
        # 获取私钥
        if not private_key:
            if chain in config["dex"] and "wallet" in config["dex"][chain]:
                private_key = config["dex"][chain]["wallet"]["private_key"]
                if not private_key:
                    print("❌ 错误: 配置文件中未找到私钥")
                    return
            else:
                print("❌ 错误: 未提供私钥，且配置文件中未找到")
                return
                
        # 创建客户端
        client = KyberSwapClient(chain=chain, private_key=private_key)
        
        # 获取代币地址
        token_address = get_token_address(chain, token)
        
        # 检查是否为原生代币
        if token_address == SUPPORTED_CHAINS[chain]["native_token_address"]:
            print(f"✅ {token.upper()}是原生代币，不需要授权")
            return
            
        # 转换数量为wei (或代币最小单位)
        # 简化处理，假设所有代币都是18位精度
        decimals = 18
        amount_in_wei = int(amount * (10 ** decimals))
        
        print(f"🔍 检查{chain}链上{token}的授权状态...")
        
        # 检查授权状态
        approved, tx_hash = await client.check_token_approval(token_address, str(amount_in_wei))
        
        if approved and not tx_hash:
            print(f"✅ 代币{token}已授权给KyberSwap路由合约")
        elif approved and tx_hash:
            print(f"✅ 代币{token}授权成功!")
            print(f"   交易哈希: {tx_hash}")
            print(f"   区块浏览器: {SUPPORTED_CHAINS[chain]['explorer_url']}/tx/{tx_hash}")
        else:
            print(f"❌ 代币{token}授权失败")
            if tx_hash:
                print(f"   交易哈希: {tx_hash}")
                print(f"   区块浏览器: {SUPPORTED_CHAINS[chain]['explorer_url']}/tx/{tx_hash}")
        
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

@cli.command(name="wrap")
@click.option("--chain", required=True, help="区块链网络 (例如: polygon, ethereum, bsc)")
@click.option("--amount", required=True, type=float, help="要包装的原生代币数量")
@click.option("--simulate/--real", default=True, help="是否模拟交易，默认为模拟模式")
@click.option("--private-key", help="私钥 (可选，默认从配置文件加载)")
@coro
async def wrap_command(chain: str, amount: float, simulate: bool, private_key: Optional[str]):
    """
    包装原生代币为包装版本（如MATIC -> WMATIC, ETH -> WETH）
    
    例如:
    python -m src.dex.KyberSwap.swap wrap --chain polygon --amount 0.1
    """
    try:
        # 加载配置
        config = load_config()
        
        # 检查私钥是否存在
        if not simulate and not private_key:
            if chain not in config["dex"]:
                print(f"❌ 找不到链 {chain} 的配置")
                return
                
            # 适配两种配置路径格式
            if "private_key" in config["dex"][chain]:
                private_key = config["dex"][chain]["private_key"]
            elif "wallet" in config["dex"][chain] and "private_key" in config["dex"][chain]["wallet"]:
                private_key = config["dex"][chain]["wallet"]["private_key"]
                
            if not private_key:
                print(f"❌ 找不到链 {chain} 的私钥配置")
                return
        else:
            # 模拟模式或已提供私钥
            if simulate:
                private_key = None
            
        print(f"📊 准备包装:")
        if chain in SUPPORTED_CHAINS:
            chain_config = SUPPORTED_CHAINS[chain]
            print(f"   从: {amount} {chain_config['symbol']}")
            print(f"   至: {amount} W{chain_config['symbol']} ({chain_config['wrapped_token_address']})")
            print(f"   链: {chain.capitalize()}")
            
            if simulate:
                print(f"   ⚠️ 模拟模式：不会执行实际交易，仅用于测试")
        else:
            print(f"❌ 不支持的链: {chain}")
            return
            
        # 创建客户端
        client = KyberSwapClient(chain=chain, private_key=None if simulate else private_key)
        
        # 计算wei值
        decimals = chain_config["decimals"]
        amount_wei = int(amount * (10 ** decimals))
        
        # 执行包装
        wrap_result = await client.wrap_native_token(
            amount=str(amount_wei),
            simulate=simulate
        )
        
        if "error" in wrap_result:
            print(f"❌ 包装失败: {wrap_result['error']}")
            return
            
        if wrap_result["status"] in ["成功", "模拟成功"]:
            if wrap_result["status"] == "模拟成功":
                print(f"✅ {wrap_result['status']}: {wrap_result['tx_hash']}")
                print(f"   注意: 这是一个模拟交易，没有在区块链上执行")
            else:
                print(f"✅ 包装成功: {wrap_result['tx_hash']}")
                print(f"   查看交易: {SUPPORTED_CHAINS[chain]['explorer_url']}/tx/{wrap_result['tx_hash']}")
            print(f"   {wrap_result['message']}")
        else:
            print(f"❌ 包装状态: {wrap_result['status']}")
            if "message" in wrap_result:
                print(f"   {wrap_result['message']}")
                
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

@cli.command(name="unwrap")
@click.option("--chain", required=True, help="区块链网络 (例如: polygon, ethereum, bsc)")
@click.option("--amount", required=True, type=float, help="要解包装的代币数量")
@click.option("--simulate/--real", default=True, help="是否模拟交易，默认为模拟模式")
@click.option("--private-key", help="私钥 (可选，默认从配置文件加载)")
@coro
async def unwrap_command(chain: str, amount: float, simulate: bool, private_key: Optional[str]):
    """
    解包装代币为原生代币（如WMATIC -> MATIC, WETH -> ETH）
    
    例如:
    python -m src.dex.KyberSwap.swap unwrap --chain polygon --amount 0.1
    """
    try:
        # 加载配置
        config = load_config()
        
        # 检查私钥是否存在
        if not simulate and not private_key:
            if chain not in config["dex"]:
                print(f"❌ 找不到链 {chain} 的配置")
                return
                
            # 适配两种配置路径格式
            if "private_key" in config["dex"][chain]:
                private_key = config["dex"][chain]["private_key"]
            elif "wallet" in config["dex"][chain] and "private_key" in config["dex"][chain]["wallet"]:
                private_key = config["dex"][chain]["wallet"]["private_key"]
                
            if not private_key:
                print(f"❌ 找不到链 {chain} 的私钥配置")
                return
        else:
            # 模拟模式或已提供私钥
            if simulate:
                private_key = None
            
        print(f"📊 准备解包装:")
        if chain in SUPPORTED_CHAINS:
            chain_config = SUPPORTED_CHAINS[chain]
            print(f"   从: {amount} W{chain_config['symbol']} ({chain_config['wrapped_token_address']})")
            print(f"   至: {amount} {chain_config['symbol']}")
            print(f"   链: {chain.capitalize()}")
            
            if simulate:
                print(f"   ⚠️ 模拟模式：不会执行实际交易，仅用于测试")
        else:
            print(f"❌ 不支持的链: {chain}")
            return
            
        # 创建客户端
        client = KyberSwapClient(chain=chain, private_key=None if simulate else private_key)
        
        # 计算wei值
        decimals = chain_config["decimals"]
        amount_wei = int(amount * (10 ** decimals))
        
        # 执行解包装
        unwrap_result = await client.unwrap_native_token(
            amount=str(amount_wei),
            simulate=simulate
        )
        
        if "error" in unwrap_result:
            print(f"❌ 解包装失败: {unwrap_result['error']}")
            return
            
        if unwrap_result["status"] in ["成功", "模拟成功"]:
            if unwrap_result["status"] == "模拟成功":
                print(f"✅ {unwrap_result['status']}: {unwrap_result['tx_hash']}")
                print(f"   注意: 这是一个模拟交易，没有在区块链上执行")
            else:
                print(f"✅ 解包装成功: {unwrap_result['tx_hash']}")
                print(f"   查看交易: {SUPPORTED_CHAINS[chain]['explorer_url']}/tx/{unwrap_result['tx_hash']}")
            print(f"   {unwrap_result['message']}")
        else:
            print(f"❌ 解包装状态: {unwrap_result['status']}")
            if "message" in unwrap_result:
                print(f"   {unwrap_result['message']}")
                
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

async def swap_tokens(chain: str, token_in: str, token_out: str, amount: float, 
                     slippage: float = 0.5, save_gas: bool = False, real: bool = False,
                     timeout: int = 60, gas_multiplier: float = 1.8, 
                     excluded_sources: str = None, recipient: str = None, skip_confirmation: bool = True) -> Dict:
    """
    执行代币交换的API函数，返回结果而不是打印到控制台
    
    Args:
        chain: 链名称 (polygon, ethereum, bsc, base)
        token_in: 输入代币地址或符号
        token_out: 输出代币地址或符号
        amount: 输入代币数量
        slippage: 滑点容忍度（百分比，如0.5表示0.5%)
        save_gas: 是否优先考虑节省gas
        real: 是否执行真实交易（否则仅模拟）
        timeout: 交易超时时间（秒）
        gas_multiplier: Gas估计值乘数
        excluded_sources: 排除的流动性来源，逗号分隔
        recipient: 代币接收地址，默认为交易发送者地址
        skip_confirmation: 是否跳过交易确认步骤
        
    Returns:
        Dict: 包含交易结果的字典
    """
    try:
        # 初始化结果字典
        result = {
            "success": False,
            "status": "未执行",
            "message": "",
            "error": None,
            "chain": chain,
            "token_in": token_in,
            "token_out": token_out,
            "amount": amount
        }
        
        # 初始化客户端
        private_key = os.environ.get("PRIVATE_KEY")
        
        # 如果环境变量中没有私钥，尝试从配置文件获取
        if not private_key:
            try:
                config = load_config()
                if chain in config["dex"]:
                    # 适配两种配置路径格式
                    if "private_key" in config["dex"][chain]:
                        private_key = config["dex"][chain]["private_key"]
                    elif "wallet" in config["dex"][chain] and "private_key" in config["dex"][chain]["wallet"]:
                        private_key = config["dex"][chain]["wallet"]["private_key"]
            except Exception as e:
                result["error"] = f"从配置文件加载私钥失败: {str(e)}"
                return result
        
        if real and not private_key:
            result["error"] = "执行实际交易需要设置PRIVATE_KEY环境变量或在配置文件中配置私钥"
            return result
            
        # 处理排除的来源
        final_excluded_sources = excluded_sources or ""
        if "bebop" not in final_excluded_sources:
            if final_excluded_sources:
                final_excluded_sources += ",bebop"
            else:
                final_excluded_sources = "bebop"
        
        # 处理各种代币输入形式
        try:
            # 输入代币标准化
            if token_in.upper() in ["polygon", "POL"] and chain.lower() == "polygon":
                token_in = "polygon"
            elif token_in.upper() == "ethereum" and chain.lower() in ["ethereum", "base"]:
                token_in = "ethereum"
            elif token_in.upper() == "BNB" and chain.lower() == "bsc":
                token_in = "BNB"
                
            # 获取代币地址
            token_in_address = get_token_address(chain, token_in)
            token_out_address = get_token_address(chain, token_out)
            
            # 获取代币精度
            token_in_decimals = get_token_decimals(chain, token_in_address)
            amount_in_wei = str(int(amount * (10 ** token_in_decimals)))
            
            # 检查是否为原生代币
            is_native_in = token_in_address.lower() == SUPPORTED_CHAINS[chain]["native_token_address"].lower()
            is_native_out = token_out_address.lower() == SUPPORTED_CHAINS[chain]["native_token_address"].lower()
            
            # 更新结果
            result.update({
                "token_in_address": token_in_address,
                "token_out_address": token_out_address,
                "amount_in_wei": amount_in_wei,
                "is_native_in": is_native_in,
                "is_native_out": is_native_out
            })
            
        except Exception as e:
            result["error"] = f"处理代币地址时出错: {str(e)}"
            return result
        
        # 创建客户端
        client = KyberSwapClient(chain, private_key)
        
        # 如果是真实交易，检查余额是否足够
        if real:
            try:
                # 获取当前余额
                if is_native_in:
                    # 原生代币余额
                    balance = client.web3.eth.get_balance(client.address)
                    formatted_balance = client.web3.from_wei(balance, 'ether')
                    formatted_amount = client.web3.from_wei(int(amount_in_wei), 'ether')
                    symbol = SUPPORTED_CHAINS[chain]["symbol"]
                    
                    if int(amount_in_wei) > balance:
                        result["error"] = f"原生代币余额不足。请求: {formatted_amount} {symbol}，可用: {formatted_balance} {symbol}"
                        result["status"] = "失败"
                        return result
                else:
                    # ERC20代币余额
                    token_balance = client.get_balance(token_in_address)
                    formatted_balance = client.format_amount_with_decimals(token_balance, token_in_address)
                    formatted_amount = client.format_amount_with_decimals(int(amount_in_wei), token_in_address)
                    token_symbol = token_in
                    
                    if isinstance(token_in, str) and token_in.startswith("0x"):
                        for symbol, addr in SUPPORTED_CHAINS[chain]["common_tokens"].items():
                            if addr.lower() == token_in.lower():
                                token_symbol = symbol
                                break
                    
                    # 计算0.1%的容差
                    tolerance = int(token_balance * 0.001)  # 0.1% = 0.001
                    amount_in_wei_int = int(amount_in_wei)
                    
                    if amount_in_wei_int > token_balance:
                        # 检查是否在容差范围内
                        if amount_in_wei_int <= (token_balance + tolerance):
                            # 在容差范围内，使用实际余额
                            print(f"⚠️ 请求金额与可用余额相差小于0.1%，自动调整为使用全部余额")
                            amount_in_wei = str(token_balance)
                            # 更新格式化后的金额显示
                            formatted_amount = client.format_amount_with_decimals(token_balance, token_in_address)
                        else:
                            # 超出容差范围，返回错误
                            result["error"] = f"代币余额不足。请求: {formatted_amount} {token_symbol}，可用: {formatted_balance} {token_symbol}"
                            result["status"] = "失败"
                            return result
            except Exception as e:
                print(f"⚠️ 检查余额时出错: {str(e)}")
                # 继续执行，让client.swap方法处理余额检查
        
        # 注意：不在这里预先获取路由信息，避免重复调用
        # client.swap方法内部会获取路由信息，我们在交易完成后从结果中提取输出金额
        
        # 执行交换，根据代币类型决定是否重试
        max_retries = 1 if token_in.upper() == "USDT" else 3  # USDT兑换其他币种时不重试
        last_error = None

        for attempt in range(max_retries):
            try:
                # 执行交换
                swap_result = await client.swap(
                    token_in=token_in_address,
                    token_out=token_out_address,
                    amount_in=amount_in_wei,
                    slippage=slippage,
                    is_native_in=is_native_in,
                    confirm=not skip_confirmation,  # 如果skip_confirmation为True，则不需要确认
                    save_gas=save_gas,
                    deadline_seconds=timeout,
                    simulate=not real,
                    gas_multiplier=gas_multiplier,
                    excluded_sources=final_excluded_sources,
                    recipient=recipient
                )
                
                # 处理结果
                result["status"] = swap_result["status"]
                result["message"] = swap_result.get("message", "")
                
                # 如果交易成功或是模拟交易，直接返回结果
                if swap_result["status"] in ["成功", "模拟成功"]:
                    result["success"] = True
                    if swap_result["status"] == "成功":
                        result["tx_hash"] = swap_result.get("tx_hash", "")
                        if "receipt" in swap_result:
                            result["receipt"] = swap_result["receipt"]
                    elif swap_result["status"] == "模拟成功":
                        result["simulated"] = True

                    # 从swap_result中提取输出金额信息（如果有的话）
                    if "route_summary" in swap_result:
                        route_summary = swap_result["route_summary"]
                        if "amountOut" in route_summary:
                            result["raw_amount_out"] = route_summary["amountOut"]
                            # 获取输出代币精度
                            token_out_decimals = get_token_decimals(chain, token_out_address)
                            # 计算格式化的输出金额
                            formatted_amount_out = int(route_summary["amountOut"]) / (10 ** token_out_decimals)
                            result["amount_out"] = formatted_amount_out
                            result["route_summary"] = route_summary

                    return result
                
                # 如果是明确的失败状态
                if swap_result["status"] == "失败":
                    last_error = swap_result.get("error", "未知错误")
                    print(f"第{attempt + 1}次尝试失败: {last_error}")
                    # 只有在非USDT交易且不是最后一次尝试时才重试
                    if token_in.upper() != "USDT" and attempt < max_retries - 1:
                        print(f"等待5秒后进行第{attempt + 2}次尝试...")
                        await asyncio.sleep(5)  # 在重试之前等待5秒
                    continue
                
                # 如果是其他状态（如pending或未知），直接返回结果
                return result
                
            except Exception as e:
                last_error = str(e)
                print(f"第{attempt + 1}次尝试发生异常: {last_error}")
                # 只有在非USDT交易且不是最后一次尝试时才重试
                if token_in.upper() != "USDT" and attempt < max_retries - 1:
                    print(f"等待5秒后进行第{attempt + 2}次尝试...")
                    await asyncio.sleep(5)
                continue
        
        # 如果所有重试都失败，返回最后一次的错误
        result["status"] = "失败"
        result["error"] = f"在{max_retries}次尝试后仍然失败: {last_error}"
        return result
        
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        print(f"执行交易时发生异常:\n{error_traceback}")
        
        return {
            "success": False,
            "status": "失败",
            "message": "执行交易时发生异常",
            "error": str(e),
            "chain": chain,
            "token_in": token_in,
            "token_out": token_out,
            "amount": amount
        }

async def get_dex_price(chain: str, token_in: str, token_out: str, amount: float) -> Dict:
    """
    快速获取DEX上的代币兑换价格，只获取交易预览信息，不进行交易模拟
    
    Args:
        chain: 链名称 (polygon, ethereum, bsc, base)
        token_in: 输入代币地址或符号
        token_out: 输出代币地址或符号
        amount: 输入代币数量
        
    Returns:
        Dict: {
            "success": bool,
            "amount_out": float,  # 输出代币数量
            "amount_out_usd": float,  # 输出金额的USD价值
            "amount_in_usd": float,  # 输入金额的USD价值
            "price_impact": float,  # 价格影响（百分比）
            "price": float,  # 每单位输入代币的输出代币价格
            "error": str  # 如果失败，错误信息
        }
    """
    try:
        # 初始化客户端
        client = KyberSwapClient(chain=chain)
        
        # 获取代币地址
        token_in_address = get_token_address(chain, token_in)
        token_out_address = get_token_address(chain, token_out)
        
        # 获取输入代币精度
        token_in_decimals = get_token_decimals(chain, token_in_address)
        amount_in_wei = str(int(amount * (10 ** token_in_decimals)))
        
        # 获取路由信息
        routes_data = await client.get_routes(
            token_in=token_in_address,
            token_out=token_out_address,
            amount_in=amount_in_wei,
            save_gas=True,
            excluded_sources="bebop",  # 默认排除bebop
            use_proxy=True  # 获取价格信息可以使用代理
        )
        
        if "data" not in routes_data or "routeSummary" not in routes_data["data"]:
            return {
                "success": False,
                "error": "无法获取路由信息"
            }
            
        route_summary = routes_data["data"]["routeSummary"]
        
        # 获取输出代币精度
        token_out_decimals = get_token_decimals(chain, token_out_address)
        
        # 计算输出金额
        amount_out = int(route_summary["amountOut"]) / (10 ** token_out_decimals)
        
        # 计算价格影响
        price_impact = 0
        amount_in_usd = float(route_summary.get("amountInUsd", 0))
        amount_out_usd = float(route_summary.get("amountOutUsd", 0))
        if amount_in_usd > 0:
            price_impact = (amount_in_usd - amount_out_usd) / amount_in_usd * 100
            
        # 计算每单位输入代币的输出代币价格
        price = amount_out / amount if amount > 0 else 0
        
        return {
            "success": True,
            "amount_out": amount_out,
            "amount_out_usd": amount_out_usd,
            "amount_in_usd": amount_in_usd,
            "price_impact": price_impact,
            "price": price,
            "route_summary": {
                "gas": route_summary.get("gas", 0),
                "gasUsd": route_summary.get("gasUsd", 0),
                "route": route_summary.get("route", [])
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

async def get_swap_preview(chain: str, token_in: str, token_out: str, amount: float, 
                         slippage: float = 0.5, save_gas: bool = False) -> Dict:
    """
    获取交易预览信息，包含详细的日志记录
    
    Args:
        chain: 链名称 (polygon, ethereum, bsc)
        token_in: 输入代币地址或符号
        token_out: 输出代币地址或符号
        amount: 输入代币数量
        slippage: 滑点容忍度（百分比）
        save_gas: 是否优先考虑节省gas
        
    Returns:
        Dict: {
            "success": bool,
            "expected_amount": float,  # 预期获得的代币数量
            "gas_cost_usd": float,    # gas成本(USD)
            "price_impact": float,     # 价格影响(%)
            "route_complexity": int,   # 路由复杂度
            "error": str              # 如果失败，错误信息
        }
    """
    try:
        print(f"\n🔍 获取交易预览...")
        print(f"   链: {chain}")
        print(f"   输入代币: {token_in}")
        print(f"   输出代币: {token_out}")
        print(f"   数量: {amount}")
        print(f"   滑点: {slippage}%")
        print(f"   节省gas: {save_gas}")
        
        # 初始化客户端
        client = KyberSwapClient(chain=chain)
        
        # 获取代币地址
        token_in_address = get_token_address(chain, token_in)
        token_out_address = get_token_address(chain, token_out)
        
        print(f"\n📍 代币地址:")
        print(f"   输入: {token_in_address}")
        print(f"   输出: {token_out_address}")
        
        # 获取输入代币精度并转换金额
        token_in_decimals = get_token_decimals(chain, token_in_address)
        amount_in_wei = str(int(amount * (10 ** token_in_decimals)))
        
        print(f"\n💱 转换后的输入金额: {amount_in_wei} wei")
        
        # 获取路由信息
        routes_data = await client.get_routes(
            token_in=token_in_address,
            token_out=token_out_address,
            amount_in=amount_in_wei,
            slippage=slippage,
            save_gas=save_gas,
            excluded_sources="bebop",  # 默认排除bebop
            is_wei_format=True,
            use_proxy=True  # 获取预览信息可以使用代理
        )
        
        if "error" in routes_data:
            print(f"\n❌ 获取路由失败: {routes_data['error']}")
            return {
                "success": False,
                "error": routes_data["error"]
            }
            
        if "data" not in routes_data or "routeSummary" not in routes_data["data"]:
            error_msg = "无法获取路由信息: 响应数据格式不正确"
            print(f"\n❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg
            }
            
        route_summary = routes_data["data"]["routeSummary"]
        
        # 获取输出代币精度
        token_out_decimals = get_token_decimals(chain, token_out_address)
        
        # 计算预期输出金额
        expected_amount = int(route_summary["amountOut"]) / (10 ** token_out_decimals)
        
        # 计算价格影响
        price_impact = 0
        amount_in_usd = float(route_summary.get("amountInUsd", 0))
        amount_out_usd = float(route_summary.get("amountOutUsd", 0))
        if amount_in_usd > 0:
            price_impact = (amount_in_usd - amount_out_usd) / amount_in_usd * 100
            
        # 计算路由复杂度
        route_complexity = 0
        if "route" in route_summary:
            for route_path in route_summary["route"]:
                route_complexity += len(route_path)
                
        # 获取gas成本
        gas_cost_usd = float(route_summary.get("gasUsd", 0))
        
        result = {
            "success": True,
            "expected_amount": expected_amount,
            "gas_cost_usd": gas_cost_usd,
            "price_impact": price_impact,
            "route_complexity": route_complexity,
            "amount_in_usd": amount_in_usd,
            "amount_out_usd": amount_out_usd
        }
        
        print("\n📊 交易预览结果:")
        print(f"   预计获得: {expected_amount:.6f} {token_out}")
        print(f"   输入价值: ${amount_in_usd:.2f}")
        print(f"   输出价值: ${amount_out_usd:.2f}")
        print(f"   价格影响: {price_impact:.2f}%")
        print(f"   Gas成本: ${gas_cost_usd:.2f}")
        print(f"   路由复杂度: {route_complexity} 跳")
        
        if route_complexity > 2:
            print(f"\n⚠️ 警告: 检测到复杂路由 ({route_complexity} 跳)，可能影响交易成功率")
            
        if price_impact > 1:
            print(f"\n⚠️ 警告: 检测到较大价格影响 ({price_impact:.2f}%)，请谨慎操作")
            
        return result
        
    except Exception as e:
        print(f"\n❌ 获取交易预览时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    cli() 